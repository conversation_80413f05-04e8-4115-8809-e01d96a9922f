package com.asmtunis.procaisseinventory.pro_caisse.client.screens.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Person
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableFloatState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client

@Composable
fun ClientInfoHeader (
    client: Client,
    toolbarHeightPx: Float,
    toolbarOffsetHeightPx: MutableFloatState,
    d: Float,
    progress: Float

) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(((toolbarHeightPx + toolbarOffsetHeightPx.floatValue) / d).dp)
                .background(MaterialTheme.colorScheme.onPrimaryContainer)
        ) {
            Row(
                modifier = Modifier.fillMaxSize(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                Spacer(modifier = Modifier.width(24.dp))
                Spacer(modifier = Modifier.fillMaxWidth().weight(progress + 0.001f))
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        modifier = Modifier
                            .padding(9.dp)
                            .then(if (progress == 1.0f) Modifier.weight(progress + 0.001f) else Modifier)
                            .aspectRatio(1f)
                            .clip(CircleShape)
                            .background(MaterialTheme.colorScheme.tertiaryContainer)
                    ) {
                        Icon(
                            imageVector = Icons.TwoTone.Person,
                            contentDescription = "",
                            modifier = Modifier.fillMaxSize()
                        )
                    }

                    Text(
                        text =client.cLINomPren + "(" + client.cLIType + ")",
                        color = MaterialTheme.colorScheme.inversePrimary,
                        modifier = Modifier
                            .alpha(progress)
                            .padding((8 * (progress * progress * progress)).dp),
                        fontSize = (20 * (progress)).sp,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                Text(
                    text = client.cLINomPren  + "(" + client.cLIType + ")",
                    color = MaterialTheme.colorScheme.inverseOnSurface,
                    modifier = Modifier
                        .alpha(1f - progress)
                       // .weight(1.001f - progress)
                        .padding(start = 9.dp),
                    fontSize = (18 * (1f - progress)).sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.weight(1f))
            }
        }


}